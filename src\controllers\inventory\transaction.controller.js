const { Types } = require('mongoose');
const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');

const { transactionsService } = require('../../services/inventory');
const { InPage, Order } = require('../../models/inventory');
const { parseFiltersSafely } = require('../../helperfunction');

const getRealTimeFilters = (req) => {
  const { filters, searchTerm } = req.query;
  const options = {
    page: parseInt(req.query.page) || 1,
    limit: parseInt(req.query.limit) || 10,
  };
  let _filterForOutstandingPo = false;

  let baseFilter = {
    profileId: req?.profile?._id,
    $or: [{ isArchived: { $exists: false } }, { isArchived: false }],
  };

  const parsedFilters = parseFiltersSafely(filters) || [];
  if (parsedFilters?.length > 0) {
    parsedFilters.forEach((filter) => {
      const { path, value, isObjectId } = filter;
      if (path === 'poExists') {
        if (value === 'true') _filterForOutstandingPo = 'true';
        else _filterForOutstandingPo = 'false';
        return;
      }
      const valArray = Array.isArray(value) ? value : [value];
      const resolvedValue = isObjectId
        ? valArray.map((v) => new Types.ObjectId(v))
        : valArray;

      baseFilter[path] = { $in: resolvedValue };
    });
  }

  const searchPartsByQuantityThreshold = (searchTerm) => {
    const quantityThreshold = parseInt(searchTerm, 10);

    // eslint-disable-next-line
    if (!isNaN(quantityThreshold)) {
      return quantityThreshold;
    }
  };

  let searchConditions = [];

  if (searchTerm) {
    const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    searchConditions.push(
      { name: { $regex: escapedSearchTerm, $options: 'i' } },
      { uom: { $regex: escapedSearchTerm, $options: 'i' } },
      { quantity: searchPartsByQuantityThreshold(searchTerm) },
      {
        batches: {
          $elemMatch: { batchNo: { $regex: escapedSearchTerm, $options: 'i' } },
        },
      }
    );

    const searchWords = searchTerm
      .split(/\s+/)
      .filter((word) => word.length > 0);
    if (searchWords.length > 1) {
      searchConditions.push({
        $and: searchWords.map((word) => ({
          name: {
            $regex: word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
            $options: 'i',
          },
        })),
      });
    }

    const normalizedSearchTerm = searchTerm.replace(/\s+/g, '').toLowerCase();
    const lowStockPattern =
      /^(lowstock|low\sstock|l|lo|low|lowS|low\sSt|low\sSto|low\sStoc|low\sStock)$/i;
    if (lowStockPattern.test(normalizedSearchTerm)) {
      searchConditions.push({
        $or: [
          { $expr: { $lte: ['$quantity', '$quantityThreshold'] } },
          { quantity: { $lte: 0 } },
        ],
      });
    }

    if (searchConditions.length > 0) {
      baseFilter.$and = [{ $or: searchConditions }];
    }
  }

  return { baseFilter, options };
};

const getTransactions = catchAsync(async (req, res) => {
  const { filters: paramFilter, searchTerm } = req.query;
  const options = {
    page: parseInt(req.query.page),
    limit: parseInt(req.query.limit),
  };

  let filters = {
    type: req.query.type,
    id: req.query.id,
    // object: req.query.object,
    profileId: req?.profile?._id,
  };

  const parsedFilters = parseFiltersSafely(paramFilter) || [];
  if (parsedFilters?.length > 0) {
    parsedFilters.forEach((filter) => {
      const { path, value } = filter;
      filters[path] = { $in: value };
    });
  }

  const compareQuantity = (searchTerm) => {
    const filteredQuantity = parseInt(searchTerm, 10);
    return !Number.isNaN(filteredQuantity)
      ? { netQuantity: filteredQuantity }
      : null;
  };

  const filteredInfo = [];

  if (searchTerm) {
    filteredInfo.push({ from: { $regex: `^${searchTerm}`, $options: 'i' } });
    filteredInfo.push({ to: { $regex: `^${searchTerm}`, $options: 'i' } });
    filteredInfo.push({ batch: { $regex: `^${searchTerm}`, $options: 'i' } });
    filteredInfo.push({ object: { $regex: `${searchTerm}`, $options: 'i' } });

    const quantityQuery = compareQuantity(searchTerm);
    if (quantityQuery !== null) {
      filteredInfo.push(quantityQuery);
    }

    const commentQuery = {
      comment: { $regex: `^${searchTerm}`, $options: 'i' },
    };
    const comments = await InPage.find(commentQuery).select('_id');
    if (comments.length > 0) {
      const commentIds = comments.map((item) => item._id);
      filteredInfo.push({ inPageId: { $in: commentIds } });
    }

    const usageQuery = { usage: { $regex: `^${searchTerm}`, $options: 'i' } };
    const usages = await Order.find(usageQuery).select('_id');
    if (usages.length > 0) {
      const usageIds = usages.map((item) => item._id);
      filteredInfo.push({ order: { $in: usageIds } });
    }
  }

  if (filteredInfo.length > 0) {
    filters.$or = filteredInfo;
  }

  const results = await transactionsService.getTransactions(filters, options);
  res.status(httpStatus.OK).send(results);
});

const getRealTimeData = catchAsync(async (req, res) => {
  const { type } = req.query;
  const { baseFilter, options } = getRealTimeFilters(req);
  const results = await transactionsService.getRealTimeData(type, baseFilter, {
    limit:
      options.limit && parseInt(options.limit, 10) > 0
        ? parseInt(options.limit, 10)
        : 10,
    page:
      options.page && parseInt(options.page, 10) > 0
        ? parseInt(options.page, 10)
        : 1,
  });

  res.status(httpStatus.OK).send(results);
});

const getRealTimeCountsData = catchAsync(async (req, res) => {
  const { type } = req.query;
  const { baseFilter, options } = getRealTimeFilters(req);
  const results = await transactionsService.getRealTimeCountsData(
    type,
    baseFilter,
    {
      limit:
        options.limit && parseInt(options.limit, 10) > 0
          ? parseInt(options.limit, 10)
          : 10,
      page:
        options.page && parseInt(options.page, 10) > 0
          ? parseInt(options.page, 10)
          : 1,
    }
  );

  res.status(httpStatus.OK).send(results);
});

const getRealTimePos = catchAsync(async (req, res) => {
  const results = await transactionsService.getRealTimePos(
    req.body.ids,
    req.profile
  );

  res.status(httpStatus.OK).send(results);
});
const getAllRealTimeData = catchAsync(async (req, res) => {
  const { type } = req.query;
  const data = await transactionsService.getRealTimeData(type, {
    profileId: req?.profile?._id,
  });
  res.status(httpStatus.OK).send(data);
});

const getScrapData = catchAsync(async (req, res) => {
  const { field_name, field_value, searchTerm } = req.query;
  const results = await transactionsService.getScrapData(
    req.profile,
    field_name,
    field_value,
    searchTerm
  );
  res.status(httpStatus.OK).send(results);
});

const getInventoryDashboardStockOut = catchAsync(async (req, res) => {
  const { page, limit, searchTerm, field_name, field_value, filters } =
    req.query;
  const profile = req.profile;
  const results = await transactionsService.getInventoryDashboardStockOut({
    profile,
    page,
    limit,
    searchTerm,
    field_name,
    field_value,
    filters,
  });
  res.status(httpStatus.OK).send(results);
});

const getInventoryDashboardStockIn = catchAsync(async (req, res) => {
  const { page, limit, searchTerm, field_name, field_value, filters } =
    req.query;
  const profile = req.profile;
  const results = await transactionsService.getInventoryDashboardStockIn({
    profile,
    page,
    limit,
    searchTerm,
    field_name,
    field_value,
    filters,
  });
  res.status(httpStatus.OK).send(results);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const { type } = req.query;
  const profile = req.profile;
  const results = await transactionsService.getFilterOptions({ profile, type });
  res.status(httpStatus.OK).send(results);
});

const exportInventoryData = catchAsync(async (req, res) => {
  const profile = req.profile;
  const filters = req.query.filters;
  const results = await transactionsService.exportInventoryData({
    profile,
    filters,
  });
  res.status(httpStatus.OK).send(results);
});

const getRealtimeFilterOptions = catchAsync(async (req, res) => {
  const { type } = req.query;
  const profile = req.profile;
  const results = await transactionsService.getRealTimeFilterOptions({
    profile,
    type,
  });
  res.status(httpStatus.OK).send(results);
});

const getTransactionFilterOptions = catchAsync(async (req, res) => {
  const profile = req.profile;
  const results = await transactionsService.getTransactionFilterOptions(
    profile
  );
  res.status(httpStatus.OK).send(results);
});

const exportRealtimeData = catchAsync(async (req, res) => {
  const profile = req.profile;
  const results = await transactionsService.exportRealtimeData(
    req.body,
    profile
  );
  res.status(httpStatus.OK).send(results);
});

module.exports = {
  getTransactions,
  getRealTimeData,
  getAllRealTimeData,
  getScrapData,
  getInventoryDashboardStockIn,
  getInventoryDashboardStockOut,
  getFilterOptions,
  exportInventoryData,
  getRealtimeFilterOptions,
  getTransactionFilterOptions,
  getRealTimeCountsData,
  getRealTimePos,
  exportRealtimeData,
};
