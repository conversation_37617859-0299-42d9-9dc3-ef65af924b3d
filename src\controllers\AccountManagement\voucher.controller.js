const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const pick = require('../../utils/pick');
const { voucherService } = require('../../services');

const paginateVouchers = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const pos = await voucherService.getPaginatedVouchers(
    req.query.filters,
    options,
    req.query.field,
    req.query.type,
    req.profile,
    req.query.searchTerm,
    req.query.voucherType
  );
  res.status(httpStatus.OK).send(pos);
});

const getVoucherById = catchAsync(async (req, res) => {
  const voucher = await voucherService.getVoucherById(
    req.params.id,
    req.profile
  );
  res.status(httpStatus.OK).send(voucher);
});

const createVoucher = catchAsync(async (req, res) => {
  const newCreate = await voucherService.createVoucher(req.body, req.profile);

  res.status(httpStatus.CREATED).send(newCreate);
});

const editVoucher = catchAsync(async (req, res) => {
  const newCreate = await voucherService.editVoucher(req.body, req.profile);

  res.status(httpStatus.OK).send(newCreate);
});

const deleteVoucher = catchAsync(async (req, res) => {
  const newCreate = await voucherService.deleteVoucher(req.body);

  res.status(httpStatus.CREATED).send(newCreate);
});

const deleteManyVoucher = catchAsync(async (req, res) => {
  const newCreate = await voucherService.deleteManyVoucher(req.body);

  res.status(httpStatus.CREATED).send(newCreate);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const filterOptions = await voucherService.getFilterOptions(
    req.profile,
    req.query.voucherType
  );
  res.status(httpStatus.OK).send(filterOptions);
});

module.exports = {
  paginateVouchers,
  createVoucher,
  deleteVoucher,
  deleteManyVoucher,
  getVoucherById,
  editVoucher,
  getFilterOptions,
};
