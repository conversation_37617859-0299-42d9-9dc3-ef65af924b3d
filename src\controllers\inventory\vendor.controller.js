const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const { vendorService } = require('../../services/inventory');
const { departmentActionsService } = require('../../services');
const { Vendor } = require('../../models/inventory');
const pick = require('../../utils/pick');
const ApiError = require('../../utils/ApiError');
const { actionTypes } = require('../../utils/Constant');
const dataGenerator = require('../../utils/chatBotDataGen');
const { parseFiltersSafely } = require('../../helperfunction');
const withTransaction = require('../../utils/withTransaction');
const { SaveMedia } = require('../../services/media.service');

const getAllVendors = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const searchQuery = req.query.q || '';
  const limit = parseInt(req.query.limit) || 25;
  const sortOrder = parseInt(req.query.sortOrder) || 1;
  const additionalFieldsArray = req.query.additionalFields || '["address"]';

  const convertStringToArray = () => {
    return additionalFieldsArray.split(',').reverse();
  };
  const fieldValues = convertStringToArray();

  const vendors = await vendorService.getAllVendors(
    page,
    searchQuery,
    limit,
    sortOrder,
    fieldValues,
    req.query.getAll === 'true',
    req.profile
  );
  res.status(httpStatus.OK).send({ vendors });
});

const queryVendors = catchAsync(async (req, res) => {
  const { filters, sortType, sortField } = req.query;
  const options = pick(req.query, ['limit', 'page']);
  const { searchTerm } = req.query;

  const query = { profileId: req?.profile?._id };
  const parsedFilters = parseFiltersSafely(filters) || [];

  if (parsedFilters.length > 0) {
    parsedFilters.forEach((filter) => {
      const { path, value, isDate } = filter;
      if (isDate) {
        query[path] = {
          $gte: new Date(value[0]),
          $lte: new Date(value[1]),
        };
      } else {
        query[path] = { $in: value };
      }
    });
  }

  // Build nameFilterConditions for basic fields
  const nameFilterConditions = [
    searchTerm ? { name: { $regex: `${searchTerm}`, $options: 'i' } } : null,
    searchTerm
      ? {
          address: {
            $elemMatch: { $regex: `${searchTerm}`, $options: 'i' },
          },
        }
      : null,
    searchTerm
      ? {
          contact: {
            $elemMatch: { $regex: `${searchTerm}`, $options: 'i' },
          },
        }
      : null,
    searchTerm
      ? {
          email: {
            $elemMatch: { $regex: `${searchTerm}`, $options: 'i' },
          },
        }
      : null,
    searchTerm ? { gstin: { $regex: `${searchTerm}`, $options: 'i' } } : null,
    searchTerm
      ? {
          billingAddress: {
            $regex: `${searchTerm}`,
            $options: 'i',
          },
        }
      : null,
    searchTerm
      ? {
          deliveryAddress: {
            $regex: `${searchTerm}`,
            $options: 'i',
          },
        }
      : null,
    searchTerm
      ? { paymentTerm: { $regex: `${searchTerm}`, $options: 'i' } }
      : null,
    searchTerm ? { id: { $regex: `${searchTerm}`, $options: 'i' } } : null,
  ].filter(Boolean);

  // Add $or condition if there are any nameFilterConditions
  if (nameFilterConditions.length > 0) {
    query.$or = nameFilterConditions;
  }

  // If field_name and field_value are provided, apply filter directly
  const result = await vendorService.queryVendors(query, {
    ...options,
    sortBy: `${sortField}:${sortType}`,
  });
  return res.status(httpStatus.OK).send(result);
});

const getVendorByName = catchAsync(async (req, res) => {
  const { name } = req.query;
  const obj = await vendorService.getVendorByName(name, req.profile);
  res.status(httpStatus.OK).send(req.isChatBot ? dataGenerator(obj) : obj);
});

const createNewVendor = catchAsync(async (req, res) => {
  const { body } = req;
  const sameVendor = await Vendor.findOne({
    name: body.name,
    profileId: req?.profile?._id,
  });
  if (sameVendor)
    throw new ApiError(httpStatus.CONFLICT, 'Vendor already exists');

  if (body?.attachments?.length > 0) {
    const mediaId = await SaveMedia(body?.attachments, req.profile);
    body.attachments = mediaId;
  }
  const vendor = await withTransaction(async (session) => {
    return vendorService.createNewVendor(
      {
        ...body,
        profileId: req?.profile?._id,
      },
      session
    );
  });
  await departmentActionsService.notifyNextDepartments(req, {
    action: 'Created a new Vendor',
    actionType: actionTypes.create,
  });

  res.status(httpStatus.OK).send({ vendor });
});

const updateVendor = catchAsync(async (req, res) => {
  const { body } = req;
  const { id } = req.params;
  const vendor = await vendorService.updateVendor(body, id, req.profile);
  vendor.isUsed = true;
  await vendor.save();
  await departmentActionsService.notifyNextDepartments(req, {
    action: 'Edited a Vendor',
    actionType: actionTypes.edit,
  });
  res.status(httpStatus.OK).send({ vendor });
});

const deleteVendor = catchAsync(async (req, res) => {
  const { id } = req.params;
  const vendor = await vendorService.deleteVendor(id);
  await departmentActionsService.notifyNextDepartments(req, {
    action: 'Deleted a Vendor',
    actionType: actionTypes.delete,
  });
  res.status(httpStatus.OK).send({ vendor });
});

const deleteManyVendors = catchAsync(async (req, res) => {
  const { ids } = req.body;
  const completed = await withTransaction(async (session) => {
    return vendorService.deleteManyVendors(ids, session);
  });
  if (completed) {
    await departmentActionsService.notifyNextDepartments(req, {
      action: 'Deleted many Vendors',
      actionType: actionTypes.delete,
    });

    res.status(httpStatus.OK).send({ msg: 'Vendors were deleted.' });
  }
});

const addManyVendors = catchAsync(async (req, res) => {
  const { data } = req.body;
  const completed = await withTransaction(async (session) => {
    return vendorService.addManyVendors(data, req.profile, session);
  });
  if (completed) {
    await departmentActionsService.notifyNextDepartments(req, {
      action: 'Created many Vendors ',
      actionType: actionTypes.create,
    });

    res.status(httpStatus.OK).send({ msg: 'Vendors were Added' });
  }
});

const updateManyVendor = catchAsync(async (req, res) => {
  const message = await withTransaction(async (session) => {
    return vendorService.updateManyVendor(req?.body, session);
  });
  res.status(httpStatus.OK).json(message);
});

const getAllVendorsForOptions = catchAsync(async (req, res) => {
  const allVendors = await vendorService.getAllVendorsForOptions(req.profile);
  res.status(httpStatus.OK).send(allVendors);
});

const getVendorsForExport = catchAsync(async (req, res) => {
  const vendors = await vendorService.getVendorsForExport(req.profile);
  res.status(httpStatus.OK).send(vendors);
});

const getFilterOptions = catchAsync(async (req, res) => {
  const filterOptions = await vendorService.getFilterOptions(req.profile);
  res.status(httpStatus.OK).send(filterOptions);
});

const getVendorById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const vendor = await vendorService.getVendorById(id);
  res.status(httpStatus.OK).send(vendor);
});

const importVendors = catchAsync(async (req, res) => {
  const { data } = req.body;
  const result = await withTransaction(async (session) => {
    return vendorService.importVendors(data, req.profile, session);
  });
  res.status(httpStatus.OK).send(result);
});

module.exports = {
  getAllVendors,
  createNewVendor,
  updateVendor,
  deleteVendor,
  deleteManyVendors,
  addManyVendors,
  queryVendors,
  updateManyVendor,
  getVendorByName,
  getAllVendorsForOptions,
  getVendorsForExport,
  getFilterOptions,
  getVendorById,
  importVendors,
};
